<x-app-layout>
    <!-- Sidebar -->
    <div class="fixed inset-y-0 left-0 z-50 w-64 bg-white border-r border-gray-200 lg:block hidden">
        <!-- Logo -->
        <div class="flex items-center h-16 px-6 border-b border-gray-200">
            <div class="flex items-center space-x-3">
                <div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                    <flux:icon name="squares-2x2" class="w-5 h-5 text-white" />
                </div>
                <span class="text-xl font-semibold text-gray-900">Dashboard</span>
            </div>
        </div>

        <!-- Navigation -->
        <nav class="flex-1 px-4 py-6 space-y-2">
            <a href="{{ route('dashboard') }}" class="flex items-center space-x-3 px-3 py-2 rounded-lg {{ request()->routeIs('dashboard') ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900' }}">
                <flux:icon name="home" class="w-5 h-5" />
                <span>Dashboard</span>
            </a>
            
            <a href="#" class="flex items-center space-x-3 px-3 py-2 rounded-lg text-gray-600 hover:bg-gray-50 hover:text-gray-900">
                <flux:icon name="chart-bar" class="w-5 h-5" />
                <span>Analytics</span>
            </a>
            
            <a href="#" class="flex items-center space-x-3 px-3 py-2 rounded-lg text-gray-600 hover:bg-gray-50 hover:text-gray-900">
                <flux:icon name="users" class="w-5 h-5" />
                <span>Users</span>
            </a>
            
            <a href="#" class="flex items-center space-x-3 px-3 py-2 rounded-lg text-gray-600 hover:bg-gray-50 hover:text-gray-900">
                <flux:icon name="cube" class="w-5 h-5" />
                <span>Products</span>
            </a>
            
            <a href="#" class="flex items-center space-x-3 px-3 py-2 rounded-lg text-gray-600 hover:bg-gray-50 hover:text-gray-900">
                <flux:icon name="shopping-bag" class="w-5 h-5" />
                <span>Orders</span>
            </a>
            
            <a href="#" class="flex items-center space-x-3 px-3 py-2 rounded-lg text-gray-600 hover:bg-gray-50 hover:text-gray-900">
                <flux:icon name="cog-6-tooth" class="w-5 h-5" />
                <span>Settings</span>
            </a>
        </nav>

        <!-- User Profile -->
        <div class="p-4 border-t border-gray-200">
            <div class="flex items-center space-x-3 px-3 py-2">
                <img src="https://fluxui.dev/img/demo/user.jpg" alt="User" class="w-8 h-8 rounded-full">
                <div class="flex-1">
                    <p class="text-sm font-medium text-gray-900">John Doe</p>
                    <p class="text-xs text-gray-500">Admin</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Mobile menu button -->
    <div class="lg:hidden fixed top-4 left-4 z-50">
        <flux:button variant="outline" size="sm" x-data @click="$dispatch('toggle-sidebar')">
            <flux:icon name="bars-3" class="w-5 h-5" />
        </flux:button>
    </div>

    <!-- Main content -->
    <div class="lg:pl-64">
        <main class="p-6">
            @livewire('dashboard')
        </main>
    </div>
</x-app-layout>
