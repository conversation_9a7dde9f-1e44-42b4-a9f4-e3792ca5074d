<x-app-layout>
    <flux:sidebar sticky class="bg-zinc-50 dark:bg-zinc-900 border-r border-zinc-200 dark:border-zinc-700">
        <flux:sidebar.toggle class="lg:hidden" icon="x-mark" />

        <flux:brand href="#" logo="https://fluxui.dev/img/demo/logo.png" name="Dashboard" class="px-2 dark:hidden" />
        <flux:brand href="#" logo="https://fluxui.dev/img/demo/logo-dark.png" name="Dashboard" class="px-2 hidden dark:flex" />

        <flux:navlist variant="outline">
            <flux:navlist.item icon="home" href="{{ route('dashboard') }}" :current="request()->routeIs('dashboard')">Dashboard</flux:navlist.item>
            <flux:navlist.item icon="chart-bar" href="#">Analytics</flux:navlist.item>
            <flux:navlist.item icon="users" href="#">Users</flux:navlist.item>
            <flux:navlist.item icon="cube" href="#">Products</flux:navlist.item>
            <flux:navlist.item icon="shopping-bag" href="#">Orders</flux:navlist.item>
            <flux:navlist.item icon="cog-6-tooth" href="#">Settings</flux:navlist.item>
        </flux:navlist>

        <flux:spacer />

        <flux:navlist variant="outline">
            <flux:navlist.item icon="information-circle" href="#">Support</flux:navlist.item>
            <flux:navlist.item icon="arrow-right-start-on-rectangle" href="#">Logout</flux:navlist.item>
        </flux:navlist>
    </flux:sidebar>

    <flux:header class="lg:hidden">
        <flux:sidebar.toggle class="lg:hidden" icon="bars-2" inset="left" />

        <flux:spacer />

        <flux:profile avatar="https://fluxui.dev/img/demo/user.jpg">
            <flux:profile.item icon="arrow-right-start-on-rectangle">Logout</flux:profile.item>
        </flux:profile>
    </flux:header>

    <flux:main>
        @livewire('dashboard')
    </flux:main>
</x-app-layout>
