<div class="space-y-6">
    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        @foreach($stats as $stat)
        <div class="bg-[#1a1a1a] border border-[#2a2a2a] rounded-lg p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-[#a3a3a3]">{{ $stat['title'] }}</p>
                    <p class="text-2xl font-bold text-white mt-2">{{ $stat['value'] }}</p>
                </div>
                <div class="w-8 h-8 bg-blue-600/10 rounded-lg flex items-center justify-center">
                    @if($stat['icon'] === 'currency-dollar')
                        <svg class="w-4 h-4 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z"/>
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z" clip-rule="evenodd"/>
                        </svg>
                    @elseif($stat['icon'] === 'users')
                        <svg class="w-4 h-4 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z"/>
                        </svg>
                    @elseif($stat['icon'] === 'credit-card')
                        <svg class="w-4 h-4 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4z"/>
                            <path fill-rule="evenodd" d="M18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z" clip-rule="evenodd"/>
                        </svg>
                    @else
                        <svg class="w-4 h-4 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                        </svg>
                    @endif
                </div>
            </div>
            <div class="flex items-center mt-4">
                <span class="text-sm font-medium text-green-400">{{ $stat['change'] }}</span>
                <span class="text-sm text-[#a3a3a3] ml-2">from last month</span>
            </div>
        </div>
        @endforeach
    </div>

    <!-- Main Content Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Chart Area -->
        <div class="lg:col-span-2 bg-[#1a1a1a] border border-[#2a2a2a] rounded-lg p-6">
            <div class="flex items-center justify-between mb-6">
                <h3 class="text-lg font-semibold text-white">Overview</h3>
                <div class="flex space-x-2">
                    <button class="px-3 py-1 text-sm bg-blue-600 text-white rounded-md">January - June 2023</button>
                </div>
            </div>
            
            <!-- Placeholder for chart -->
            <div class="h-80 bg-[#0f0f0f] rounded-lg border border-[#2a2a2a] flex items-center justify-center">
                <div class="text-center">
                    <svg class="w-16 h-16 text-[#a3a3a3] mx-auto mb-4" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z"/>
                    </svg>
                    <p class="text-[#a3a3a3]">Chart visualization would go here</p>
                    <p class="text-sm text-[#666] mt-2">Revenue trends over time</p>
                </div>
            </div>
        </div>

        <!-- Recent Sales -->
        <div class="bg-[#1a1a1a] border border-[#2a2a2a] rounded-lg p-6">
            <div class="flex items-center justify-between mb-6">
                <h3 class="text-lg font-semibold text-white">Recent Sales</h3>
                <span class="text-sm text-[#a3a3a3]">You made 265 sales this month.</span>
            </div>
            
            <div class="space-y-4">
                @foreach($recentOrders as $order)
                <div class="flex items-center space-x-4">
                    <div class="w-9 h-9 bg-blue-600/10 rounded-full flex items-center justify-center">
                        <svg class="w-4 h-4 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"/>
                        </svg>
                    </div>
                    <div class="flex-1 min-w-0">
                        <p class="text-sm font-medium text-white truncate">{{ $order['customer'] }}</p>
                        <p class="text-sm text-[#a3a3a3] truncate">{{ $order['email'] }}</p>
                    </div>
                    <div class="text-right">
                        <p class="text-sm font-medium text-white">{{ $order['amount'] }}</p>
                    </div>
                </div>
                @endforeach
            </div>
        </div>
    </div>

    <!-- Recent Orders Table -->
    <div class="bg-[#1a1a1a] border border-[#2a2a2a] rounded-lg">
        <div class="px-6 py-4 border-b border-[#2a2a2a]">
            <h3 class="text-lg font-semibold text-white">Recent Orders</h3>
            <p class="text-sm text-[#a3a3a3] mt-1">A list of your recent orders.</p>
        </div>
        
        <div class="overflow-x-auto">
            <table class="w-full">
                <thead class="bg-[#0f0f0f]">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-[#a3a3a3] uppercase tracking-wider">Order</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-[#a3a3a3] uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-[#a3a3a3] uppercase tracking-wider">Customer</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-[#a3a3a3] uppercase tracking-wider">Date</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-[#a3a3a3] uppercase tracking-wider">Amount</th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-[#2a2a2a]">
                    @foreach($recentOrders as $order)
                    <tr class="hover:bg-[#0f0f0f]">
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-white">{{ $order['id'] }}</td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                {{ $order['status'] }}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div>
                                <div class="text-sm font-medium text-white">{{ $order['customer'] }}</div>
                                <div class="text-sm text-[#a3a3a3]">{{ $order['email'] }}</div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-[#a3a3a3]">{{ $order['date'] }}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-white">{{ $order['amount'] }}</td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    </div>
</div>
