<div>
    <flux:heading size="xl" class="mb-6">Dashboard</flux:heading>

    <!-- Search Bar -->
    <div class="mb-6">
        <flux:input 
            wire:model.live="searchQuery" 
            wire:keydown.enter="search"
            placeholder="Search..." 
            icon="magnifying-glass"
            class="max-w-md"
        />
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        @foreach($stats as $stat)
        <flux:card class="p-6">
            <div class="flex items-center justify-between">
                <div>
                    <flux:subheading class="text-zinc-500">{{ $stat['title'] }}</flux:subheading>
                    <flux:heading size="lg" class="mt-2">{{ $stat['value'] }}</flux:heading>
                </div>
                <div class="w-8 h-8 bg-blue-50 rounded-lg flex items-center justify-center">
                    @if($stat['icon'] === 'currency-dollar')
                        <flux:icon.currency-dollar class="w-4 h-4 text-blue-600" />
                    @elseif($stat['icon'] === 'users')
                        <flux:icon.users class="w-4 h-4 text-blue-600" />
                    @elseif($stat['icon'] === 'credit-card')
                        <flux:icon.credit-card class="w-4 h-4 text-blue-600" />
                    @else
                        <flux:icon.signal class="w-4 h-4 text-blue-600" />
                    @endif
                </div>
            </div>
            <div class="flex items-center mt-4">
                <span class="text-sm font-medium text-green-600">{{ $stat['change'] }}</span>
                <span class="text-sm text-zinc-500 ml-2">from last month</span>
            </div>
        </flux:card>
        @endforeach
    </div>

    <!-- Main Content Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
        <!-- Chart Area -->
        <flux:card class="lg:col-span-2 p-6">
            <div class="flex items-center justify-between mb-6">
                <flux:heading size="lg">Overview</flux:heading>
                <flux:dropdown>
                    <flux:button variant="primary" size="sm">{{ $selectedPeriod }}</flux:button>
                    
                    <flux:menu>
                        <flux:menu.item wire:click="changePeriod('January - June 2023')">January - June 2023</flux:menu.item>
                        <flux:menu.item wire:click="changePeriod('July - December 2023')">July - December 2023</flux:menu.item>
                        <flux:menu.item wire:click="changePeriod('Full Year 2023')">Full Year 2023</flux:menu.item>
                    </flux:menu>
                </flux:dropdown>
            </div>
            
            <!-- Placeholder for chart -->
            <div class="h-80 bg-zinc-50 rounded-lg border border-zinc-200 flex items-center justify-center">
                <div class="text-center">
                    <flux:icon.chart-bar class="w-16 h-16 text-zinc-400 mx-auto mb-4" />
                    <flux:subheading class="text-zinc-500">Chart visualization would go here</flux:subheading>
                    <flux:text class="text-zinc-400 mt-2">Revenue trends over time</flux:text>
                </div>
            </div>
        </flux:card>

        <!-- Recent Sales -->
        <flux:card class="p-6">
            <div class="flex items-center justify-between mb-6">
                <flux:heading size="lg">Recent Sales</flux:heading>
                <flux:subheading class="text-zinc-500">You made 265 sales this month.</flux:subheading>
            </div>
            
            <div class="space-y-4">
                @foreach($recentSales as $sale)
                <div class="flex items-center space-x-4">
                    <flux:avatar size="sm" src="{{ $sale['avatar'] }}" />
                    <div class="flex-1 min-w-0">
                        <flux:text class="font-medium truncate">{{ $sale['name'] }}</flux:text>
                        <flux:text variant="subtle" class="truncate">{{ $sale['email'] }}</flux:text>
                    </div>
                    <div class="text-right">
                        <flux:text class="font-medium">{{ $sale['amount'] }}</flux:text>
                    </div>
                </div>
                @endforeach
            </div>
        </flux:card>
    </div>

    <!-- Recent Orders Table -->
    <flux:card>
        <flux:card.header>
            <flux:heading size="lg">Recent Orders</flux:heading>
            <flux:subheading>A list of your recent orders.</flux:subheading>
        </flux:card.header>

        <flux:table>
            <flux:columns>
                <flux:column>Order</flux:column>
                <flux:column>Status</flux:column>
                <flux:column>Customer</flux:column>
                <flux:column>Date</flux:column>
                <flux:column>Amount</flux:column>
            </flux:columns>

            <flux:rows>
                @foreach($recentOrders as $order)
                <flux:row>
                    <flux:cell variant="strong">{{ $order['id'] }}</flux:cell>
                    <flux:cell><flux:badge color="green" size="sm">{{ $order['status'] }}</flux:badge></flux:cell>
                    <flux:cell>
                        <div class="flex items-center gap-3">
                            <flux:avatar size="xs" src="{{ $order['avatar'] }}" />
                            <div>
                                <div class="font-medium">{{ $order['customer'] }}</div>
                                <div class="text-sm text-zinc-500">{{ $order['email'] }}</div>
                            </div>
                        </div>
                    </flux:cell>
                    <flux:cell variant="subtle">{{ $order['date'] }}</flux:cell>
                    <flux:cell variant="strong">{{ $order['amount'] }}</flux:cell>
                </flux:row>
                @endforeach
            </flux:rows>
        </flux:table>
    </flux:card>
</div>
