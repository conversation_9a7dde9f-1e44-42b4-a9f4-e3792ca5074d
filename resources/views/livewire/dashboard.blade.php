<div>
    <flux:heading size="xl" class="mb-6">Dashboard</flux:heading>

    <!-- Search Bar -->
    <div class="mb-6">
        <flux:input
            wire:model.live="searchQuery"
            wire:keydown.enter="search"
            placeholder="Search..."
            icon="magnifying-glass"
            class="max-w-md"
        />
    </div>

{{--    <!-- Stats Cards -->--}}
{{--    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">--}}
{{--        @foreach($stats as $stat)--}}
{{--        <flux:card class="p-6">--}}
{{--            <div class="flex items-center justify-between">--}}
{{--                <div>--}}
{{--                    <flux:subheading class="text-zinc-500">{{ $stat['title'] }}</flux:subheading>--}}
{{--                    <flux:heading size="lg" class="mt-2">{{ $stat['value'] }}</flux:heading>--}}
{{--                </div>--}}
{{--                <div class="w-8 h-8 bg-blue-50 rounded-lg flex items-center justify-center">--}}
{{--                    @if($stat['icon'] === 'currency-dollar')--}}
{{--                        <flux:icon.currency-dollar class="w-4 h-4 text-blue-600" />--}}
{{--                    @elseif($stat['icon'] === 'users')--}}
{{--                        <flux:icon.users class="w-4 h-4 text-blue-600" />--}}
{{--                    @elseif($stat['icon'] === 'credit-card')--}}
{{--                        <flux:icon.credit-card class="w-4 h-4 text-blue-600" />--}}
{{--                    @else--}}
{{--                        <flux:icon.signal class="w-4 h-4 text-blue-600" />--}}
{{--                    @endif--}}
{{--                </div>--}}
{{--            </div>--}}
{{--            <div class="flex items-center mt-4">--}}
{{--                <span class="text-sm font-medium text-green-600">{{ $stat['change'] }}</span>--}}
{{--                <span class="text-sm text-zinc-500 ml-2">from last month</span>--}}
{{--            </div>--}}
{{--        </flux:card>--}}
{{--        @endforeach--}}
{{--    </div>--}}

{{--    <!-- Charts Section -->--}}
{{--    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">--}}
{{--        <!-- Revenue Chart -->--}}
{{--        <flux:card class="p-6">--}}
{{--            <div class="flex items-center justify-between mb-6">--}}
{{--                <flux:heading size="lg">Revenue Overview</flux:heading>--}}
{{--                <flux:dropdown>--}}
{{--                    <flux:button variant="primary" size="sm">{{ $selectedPeriod }}</flux:button>--}}

{{--                    <flux:menu>--}}
{{--                        <flux:menu.item wire:click="changePeriod('January - June 2023')">January - June 2023</flux:menu.item>--}}
{{--                        <flux:menu.item wire:click="changePeriod('July - December 2023')">July - December 2023</flux:menu.item>--}}
{{--                        <flux:menu.item wire:click="changePeriod('Full Year 2023')">Full Year 2023</flux:menu.item>--}}
{{--                    </flux:menu>--}}
{{--                </flux:dropdown>--}}
{{--            </div>--}}

{{--            <!-- Revenue Chart -->--}}
{{--            <div class="h-80 relative">--}}
{{--                <canvas id="revenueChart" class="w-full h-full"></canvas>--}}
{{--            </div>--}}
{{--        </flux:card>--}}

{{--        <!-- Sales Chart -->--}}
{{--        <flux:card class="p-6">--}}
{{--            <div class="flex items-center justify-between mb-6">--}}
{{--                <flux:heading size="lg">Sales Performance</flux:heading>--}}
{{--                <flux:button variant="outline" size="sm" wire:click="refreshCharts">--}}
{{--                    <flux:icon.arrow-path class="w-4 h-4 mr-2" />--}}
{{--                    Refresh--}}
{{--                </flux:button>--}}
{{--            </div>--}}

{{--            <!-- Sales Chart -->--}}
{{--            <div class="h-80 relative">--}}
{{--                <canvas id="salesChart" class="w-full h-full"></canvas>--}}
{{--            </div>--}}
{{--        </flux:card>--}}
{{--    </div>--}}

{{--    <!-- Main Content Grid -->--}}
{{--    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">--}}
{{--        <!-- Quick Stats -->--}}
{{--        <flux:card class="lg:col-span-2 p-6">--}}
{{--            <flux:heading size="lg" class="mb-6">Quick Analytics</flux:heading>--}}

{{--            <div class="grid grid-cols-2 gap-4">--}}
{{--                <div class="text-center p-4 bg-blue-50 rounded-lg">--}}
{{--                    <flux:heading size="xl" class="text-blue-600">$45.2K</flux:heading>--}}
{{--                    <flux:text class="text-blue-600 font-medium">Monthly Revenue</flux:text>--}}
{{--                    <flux:text variant="subtle" class="text-sm">+12% from last month</flux:text>--}}
{{--                </div>--}}
{{--                <div class="text-center p-4 bg-green-50 rounded-lg">--}}
{{--                    <flux:heading size="xl" class="text-green-600">2,350</flux:heading>--}}
{{--                    <flux:text class="text-green-600 font-medium">Total Orders</flux:text>--}}
{{--                    <flux:text variant="subtle" class="text-sm">+8% from last month</flux:text>--}}
{{--                </div>--}}
{{--                <div class="text-center p-4 bg-purple-50 rounded-lg">--}}
{{--                    <flux:heading size="xl" class="text-purple-600">89.5%</flux:heading>--}}
{{--                    <flux:text class="text-purple-600 font-medium">Customer Satisfaction</flux:text>--}}
{{--                    <flux:text variant="subtle" class="text-sm">+2.1% from last month</flux:text>--}}
{{--                </div>--}}
{{--                <div class="text-center p-4 bg-orange-50 rounded-lg">--}}
{{--                    <flux:heading size="xl" class="text-orange-600">573</flux:heading>--}}
{{--                    <flux:text class="text-orange-600 font-medium">Active Users</flux:text>--}}
{{--                    <flux:text variant="subtle" class="text-sm">+15% from last hour</flux:text>--}}
{{--                </div>--}}
{{--            </div>--}}
{{--        </flux:card>--}}

{{--        <!-- Recent Sales -->--}}
{{--        <flux:card class="p-6">--}}
{{--            <div class="flex items-center justify-between mb-6">--}}
{{--                <flux:heading size="lg">Recent Sales</flux:heading>--}}
{{--                <flux:subheading class="text-zinc-500">You made 265 sales this month.</flux:subheading>--}}
{{--            </div>--}}

{{--            <div class="space-y-4">--}}
{{--                @foreach($recentSales as $sale)--}}
{{--                <div class="flex items-center space-x-4">--}}
{{--                    <flux:avatar size="sm" src="{{ $sale['avatar'] }}" />--}}
{{--                    <div class="flex-1 min-w-0">--}}
{{--                        <flux:text class="font-medium truncate">{{ $sale['name'] }}</flux:text>--}}
{{--                        <flux:text variant="subtle" class="truncate">{{ $sale['email'] }}</flux:text>--}}
{{--                    </div>--}}
{{--                    <div class="text-right">--}}
{{--                        <flux:text class="font-medium">{{ $sale['amount'] }}</flux:text>--}}
{{--                    </div>--}}
{{--                </div>--}}
{{--                @endforeach--}}
{{--            </div>--}}
{{--        </flux:card>--}}
{{--    </div>--}}

{{--    <!-- Recent Orders Table -->--}}
{{--    <flux:card>--}}
{{--        <flux:card.header>--}}
{{--            <flux:heading size="lg">Recent Orders</flux:heading>--}}
{{--            <flux:subheading>A list of your recent orders.</flux:subheading>--}}
{{--        </flux:card.header>--}}

{{--        <flux:table>--}}
{{--            <flux:columns>--}}
{{--                <flux:column>Order</flux:column>--}}
{{--                <flux:column>Status</flux:column>--}}
{{--                <flux:column>Customer</flux:column>--}}
{{--                <flux:column>Date</flux:column>--}}
{{--                <flux:column>Amount</flux:column>--}}
{{--            </flux:columns>--}}

{{--            <flux:rows>--}}
{{--                @foreach($recentOrders as $order)--}}
{{--                <flux:row>--}}
{{--                    <flux:cell variant="strong">{{ $order['id'] }}</flux:cell>--}}
{{--                    <flux:cell><flux:badge color="green" size="sm">{{ $order['status'] }}</flux:badge></flux:cell>--}}
{{--                    <flux:cell>--}}
{{--                        <div class="flex items-center gap-3">--}}
{{--                            <flux:avatar size="xs" src="{{ $order['avatar'] }}" />--}}
{{--                            <div>--}}
{{--                                <div class="font-medium">{{ $order['customer'] }}</div>--}}
{{--                                <div class="text-sm text-zinc-500">{{ $order['email'] }}</div>--}}
{{--                            </div>--}}
{{--                        </div>--}}
{{--                    </flux:cell>--}}
{{--                    <flux:cell variant="subtle">{{ $order['date'] }}</flux:cell>--}}
{{--                    <flux:cell variant="strong">{{ $order['amount'] }}</flux:cell>--}}
{{--                </flux:row>--}}
{{--                @endforeach--}}
{{--            </flux:rows>--}}
{{--        </flux:table>--}}
{{--    </flux:card>--}}

{{--    <!-- Chart Initialization Script -->--}}
{{--    <script>--}}
{{--        document.addEventListener('DOMContentLoaded', function() {--}}
{{--            // Initialize charts when page loads--}}
{{--            if (typeof window.createRevenueChart === 'function') {--}}
{{--                window.revenueChart = window.createRevenueChart('revenueChart');--}}
{{--            }--}}
{{--            if (typeof window.createSalesChart === 'function') {--}}
{{--                window.salesChart = window.createSalesChart('salesChart');--}}
{{--            }--}}
{{--        });--}}

{{--        // Listen for Livewire events--}}
{{--        document.addEventListener('livewire:init', () => {--}}
{{--            Livewire.on('period-changed', (period) => {--}}
{{--                console.log('Period changed to:', period);--}}
{{--                // Update chart data based on period--}}
{{--                if (window.revenueChart) {--}}
{{--                    // Update chart data here--}}
{{--                    window.revenueChart.update();--}}
{{--                }--}}
{{--            });--}}

{{--            Livewire.on('refresh-charts', () => {--}}
{{--                console.log('Refreshing charts...');--}}
{{--                if (window.revenueChart) {--}}
{{--                    window.revenueChart.update();--}}
{{--                }--}}
{{--                if (window.salesChart) {--}}
{{--                    window.salesChart.update();--}}
{{--                }--}}
{{--            });--}}
{{--        });--}}
{{--    </script>--}}
</div>
