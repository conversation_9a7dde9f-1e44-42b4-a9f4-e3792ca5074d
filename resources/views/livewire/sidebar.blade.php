<div class="flex flex-col h-full">
    <!-- Logo/Brand -->
    <div class="p-6 border-b border-[#2a2a2a]">
        <div class="flex items-center space-x-3">
            <div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
                </svg>
            </div>
            <span class="text-xl font-semibold text-white">Dashboard</span>
        </div>
    </div>

    <!-- Navigation Menu -->
    <nav class="flex-1 p-4 space-y-2">
        <!-- Dashboard -->
        <a href="#"
           wire:click="setActiveMenu('dashboard')"
           class="flex items-center space-x-3 px-3 py-2 rounded-lg transition-colors {{ $activeMenu === 'dashboard' ? 'bg-blue-600 text-white' : 'text-[#a3a3a3] hover:bg-[#2a2a2a] hover:text-white' }}">
            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
            </svg>
            <span>Dashboard</span>
        </a>

        <!-- Analytics -->
        <a href="#"
           wire:click="setActiveMenu('analytics')"
           class="flex items-center space-x-3 px-3 py-2 rounded-lg transition-colors {{ $activeMenu === 'analytics' ? 'bg-blue-600 text-white' : 'text-[#a3a3a3] hover:bg-[#2a2a2a] hover:text-white' }}">
            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                <path d="M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z"/>
            </svg>
            <span>Analytics</span>
        </a>

        <!-- Users -->
        <a href="#"
           wire:click="setActiveMenu('users')"
           class="flex items-center space-x-3 px-3 py-2 rounded-lg transition-colors {{ $activeMenu === 'users' ? 'bg-blue-600 text-white' : 'text-[#a3a3a3] hover:bg-[#2a2a2a] hover:text-white' }}">
            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                <path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z"/>
            </svg>
            <span>Users</span>
        </a>

        <!-- Products -->
        <a href="#"
           wire:click="setActiveMenu('products')"
           class="flex items-center space-x-3 px-3 py-2 rounded-lg transition-colors {{ $activeMenu === 'products' ? 'bg-blue-600 text-white' : 'text-[#a3a3a3] hover:bg-[#2a2a2a] hover:text-white' }}">
            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 2L3 7v11a1 1 0 001 1h12a1 1 0 001-1V7l-7-5zM6 9a1 1 0 112 0v6a1 1 0 11-2 0V9zm6 0a1 1 0 112 0v6a1 1 0 11-2 0V9z" clip-rule="evenodd"/>
            </svg>
            <span>Products</span>
        </a>

        <!-- Orders -->
        <a href="#"
           wire:click="setActiveMenu('orders')"
           class="flex items-center space-x-3 px-3 py-2 rounded-lg transition-colors {{ $activeMenu === 'orders' ? 'bg-blue-600 text-white' : 'text-[#a3a3a3] hover:bg-[#2a2a2a] hover:text-white' }}">
            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                <path d="M3 1a1 1 0 000 2h1.22l.305 1.222a.997.997 0 00.01.042l1.358 5.43-.893.892C3.74 11.846 4.632 14 6.414 14H15a1 1 0 000-2H6.414l1-1H14a1 1 0 00.894-.553l3-6A1 1 0 0017 3H6.28l-.31-1.243A1 1 0 005 1H3zM16 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0zM6.5 18a1.5 1.5 0 100-3 1.5 1.5 0 000 3z"/>
            </svg>
            <span>Orders</span>
        </a>

        <!-- Settings -->
        <a href="#"
           wire:click="setActiveMenu('settings')"
           class="flex items-center space-x-3 px-3 py-2 rounded-lg transition-colors {{ $activeMenu === 'settings' ? 'bg-blue-600 text-white' : 'text-[#a3a3a3] hover:bg-[#2a2a2a] hover:text-white' }}">
            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clip-rule="evenodd"/>
            </svg>
            <span>Settings</span>
        </a>
    </nav>

    <!-- User Profile -->
    <div class="p-4 border-t border-[#2a2a2a]">
        <div class="flex items-center space-x-3 px-3 py-2">
            <div class="w-8 h-8 bg-gray-600 rounded-full flex items-center justify-center">
                <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"/>
                </svg>
            </div>
            <div class="flex-1">
                <p class="text-sm font-medium text-white">John Doe</p>
                <p class="text-xs text-[#a3a3a3]">Admin</p>
            </div>
        </div>
    </div>
</div>
