<div class="flex items-center justify-between">
    <!-- Left side - Search -->
    <div class="flex items-center space-x-4">
        <h1 class="text-xl font-semibold text-white">Dashboard</h1>

        <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg class="h-5 w-5 text-[#a3a3a3]" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd"/>
                </svg>
            </div>
            <input
                type="text"
                wire:model.live="searchQuery"
                wire:keydown.enter="search"
                placeholder="Search..."
                class="block w-80 pl-10 pr-3 py-2 border border-[#2a2a2a] rounded-lg bg-[#0f0f0f] text-white placeholder-[#a3a3a3] focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
        </div>
    </div>

    <!-- Right side - User menu and notifications -->
    <div class="flex items-center space-x-4">
        <!-- Notifications -->
        <button class="relative p-2 text-[#a3a3a3] hover:text-white transition-colors">
            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                <path d="M10 2a6 6 0 00-6 6v3.586l-.707.707A1 1 0 004 14h12a1 1 0 00.707-1.707L16 11.586V8a6 6 0 00-6-6zM10 18a3 3 0 01-3-3h6a3 3 0 01-3 3z"/>
            </svg>
            <!-- Notification badge -->
            <span class="absolute top-0 right-0 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white transform translate-x-1/2 -translate-y-1/2 bg-red-600 rounded-full">3</span>
        </button>

        <!-- User menu -->
        <div class="relative" x-data="{ open: false }">
            <button @click="open = !open" class="flex items-center space-x-3 p-2 rounded-lg hover:bg-[#2a2a2a] transition-colors">
                <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                    <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"/>
                    </svg>
                </div>
                <div class="text-left">
                    <p class="text-sm font-medium text-white">John Doe</p>
                    <p class="text-xs text-[#a3a3a3]">Administrator</p>
                </div>
                <svg class="w-4 h-4 text-[#a3a3a3]" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"/>
                </svg>
            </button>

            <!-- Dropdown menu -->
            <div x-show="open" @click.away="open = false" x-transition class="absolute right-0 mt-2 w-48 bg-[#1a1a1a] rounded-lg shadow-lg border border-[#2a2a2a] z-50">
                <div class="py-1">
                    <a href="#" class="block px-4 py-2 text-sm text-[#a3a3a3] hover:bg-[#2a2a2a] hover:text-white">Profile</a>
                    <a href="#" class="block px-4 py-2 text-sm text-[#a3a3a3] hover:bg-[#2a2a2a] hover:text-white">Settings</a>
                    <div class="border-t border-[#2a2a2a]"></div>
                    <a href="#" class="block px-4 py-2 text-sm text-[#a3a3a3] hover:bg-[#2a2a2a] hover:text-white">Sign out</a>
                </div>
            </div>
        </div>
    </div>
</div>
