<x-app-layout>
    <flux:sidebar sticky class="bg-zinc-50 dark:bg-zinc-900 border-r border-zinc-200 dark:border-zinc-700">
        <flux:sidebar.toggle class="lg:hidden" icon="x-mark" />

        <flux:brand href="#" logo="https://fluxui.dev/img/demo/logo.png" name="Dashboard" class="px-2 dark:hidden" />
        <flux:brand href="#" logo="https://fluxui.dev/img/demo/logo-dark.png" name="Dashboard" class="px-2 hidden dark:flex" />

        <flux:navlist variant="outline">
            <flux:navlist.item icon="home" href="{{ route('dashboard') }}" :current="request()->routeIs('dashboard')">Dashboard</flux:navlist.item>
            <flux:navlist.item icon="chart-bar" href="#">Analytics</flux:navlist.item>
            <flux:navlist.item icon="users" href="#">Users</flux:navlist.item>
            <flux:navlist.item icon="cube" href="#">Products</flux:navlist.item>
            <flux:navlist.item icon="shopping-bag" href="#">Orders</flux:navlist.item>
            <flux:navlist.item icon="cog-6-tooth" href="#">Settings</flux:navlist.item>
        </flux:navlist>

        <flux:spacer />

        <flux:navlist variant="outline">
            <flux:navlist.item icon="information-circle" href="#">Support</flux:navlist.item>
            <flux:navlist.item icon="arrow-right-start-on-rectangle" href="#">Logout</flux:navlist.item>
        </flux:navlist>
    </flux:sidebar>

    <flux:header class="lg:hidden">
        <flux:sidebar.toggle class="lg:hidden" icon="bars-2" inset="left" />

        <flux:spacer />

        <flux:profile avatar="https://fluxui.dev/img/demo/user.jpg">
            <flux:profile.item icon="arrow-right-start-on-rectangle">Logout</flux:profile.item>
        </flux:profile>
    </flux:header>

    <flux:main>
        <flux:heading size="xl" class="mb-6">Dashboard</flux:heading>

        <!-- Stats Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <flux:card class="p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <flux:subheading class="text-zinc-500">Total Revenue</flux:subheading>
                        <flux:heading size="lg" class="mt-2">$45,231.89</flux:heading>
                    </div>
                    <div class="w-8 h-8 bg-blue-50 rounded-lg flex items-center justify-center">
                        <flux:icon.currency-dollar class="w-4 h-4 text-blue-600" />
                    </div>
                </div>
                <div class="flex items-center mt-4">
                    <span class="text-sm font-medium text-green-600">+20.1%</span>
                    <span class="text-sm text-zinc-500 ml-2">from last month</span>
                </div>
            </flux:card>

            <flux:card class="p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <flux:subheading class="text-zinc-500">Subscriptions</flux:subheading>
                        <flux:heading size="lg" class="mt-2">+2350</flux:heading>
                    </div>
                    <div class="w-8 h-8 bg-blue-50 rounded-lg flex items-center justify-center">
                        <flux:icon.users class="w-4 h-4 text-blue-600" />
                    </div>
                </div>
                <div class="flex items-center mt-4">
                    <span class="text-sm font-medium text-green-600">+180.1%</span>
                    <span class="text-sm text-zinc-500 ml-2">from last month</span>
                </div>
            </flux:card>

            <flux:card class="p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <flux:subheading class="text-zinc-500">Sales</flux:subheading>
                        <flux:heading size="lg" class="mt-2">+12,234</flux:heading>
                    </div>
                    <div class="w-8 h-8 bg-blue-50 rounded-lg flex items-center justify-center">
                        <flux:icon.credit-card class="w-4 h-4 text-blue-600" />
                    </div>
                </div>
                <div class="flex items-center mt-4">
                    <span class="text-sm font-medium text-green-600">+19%</span>
                    <span class="text-sm text-zinc-500 ml-2">from last month</span>
                </div>
            </flux:card>

            <flux:card class="p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <flux:subheading class="text-zinc-500">Active Now</flux:subheading>
                        <flux:heading size="lg" class="mt-2">+573</flux:heading>
                    </div>
                    <div class="w-8 h-8 bg-blue-50 rounded-lg flex items-center justify-center">
                        <flux:icon.signal class="w-4 h-4 text-blue-600" />
                    </div>
                </div>
                <div class="flex items-center mt-4">
                    <span class="text-sm font-medium text-green-600">+201</span>
                    <span class="text-sm text-zinc-500 ml-2">since last hour</span>
                </div>
            </flux:card>
        </div>

        <!-- Main Content Grid -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
            <!-- Chart Area -->
            <flux:card class="lg:col-span-2 p-6">
                <div class="flex items-center justify-between mb-6">
                    <flux:heading size="lg">Overview</flux:heading>
                    <flux:button variant="primary" size="sm">January - June 2023</flux:button>
                </div>
                
                <!-- Placeholder for chart -->
                <div class="h-80 bg-zinc-50 rounded-lg border border-zinc-200 flex items-center justify-center">
                    <div class="text-center">
                        <flux:icon.chart-bar class="w-16 h-16 text-zinc-400 mx-auto mb-4" />
                        <flux:subheading class="text-zinc-500">Chart visualization would go here</flux:subheading>
                        <flux:text class="text-zinc-400 mt-2">Revenue trends over time</flux:text>
                    </div>
                </div>
            </flux:card>

            <!-- Recent Sales -->
            <flux:card class="p-6">
                <div class="flex items-center justify-between mb-6">
                    <flux:heading size="lg">Recent Sales</flux:heading>
                    <flux:subheading class="text-zinc-500">You made 265 sales this month.</flux:subheading>
                </div>
                
                <div class="space-y-4">
                    <div class="flex items-center space-x-4">
                        <flux:avatar size="sm" src="https://fluxui.dev/img/demo/user.jpg" />
                        <div class="flex-1 min-w-0">
                            <flux:text class="font-medium truncate">Olivia Martin</flux:text>
                            <flux:text variant="subtle" class="truncate"><EMAIL></flux:text>
                        </div>
                        <div class="text-right">
                            <flux:text class="font-medium">$42.25</flux:text>
                        </div>
                    </div>

                    <div class="flex items-center space-x-4">
                        <flux:avatar size="sm" src="https://fluxui.dev/img/demo/user.jpg" />
                        <div class="flex-1 min-w-0">
                            <flux:text class="font-medium truncate">Ava Johnson</flux:text>
                            <flux:text variant="subtle" class="truncate"><EMAIL></flux:text>
                        </div>
                        <div class="text-right">
                            <flux:text class="font-medium">$74.99</flux:text>
                        </div>
                    </div>

                    <div class="flex items-center space-x-4">
                        <flux:avatar size="sm" src="https://fluxui.dev/img/demo/user.jpg" />
                        <div class="flex-1 min-w-0">
                            <flux:text class="font-medium truncate">Michael Johnson</flux:text>
                            <flux:text variant="subtle" class="truncate"><EMAIL></flux:text>
                        </div>
                        <div class="text-right">
                            <flux:text class="font-medium">$64.75</flux:text>
                        </div>
                    </div>

                    <div class="flex items-center space-x-4">
                        <flux:avatar size="sm" src="https://fluxui.dev/img/demo/user.jpg" />
                        <div class="flex-1 min-w-0">
                            <flux:text class="font-medium truncate">Lisa Anderson</flux:text>
                            <flux:text variant="subtle" class="truncate"><EMAIL></flux:text>
                        </div>
                        <div class="text-right">
                            <flux:text class="font-medium">$34.50</flux:text>
                        </div>
                    </div>

                    <div class="flex items-center space-x-4">
                        <flux:avatar size="sm" src="https://fluxui.dev/img/demo/user.jpg" />
                        <div class="flex-1 min-w-0">
                            <flux:text class="font-medium truncate">Chris Lee</flux:text>
                            <flux:text variant="subtle" class="truncate"><EMAIL></flux:text>
                        </div>
                        <div class="text-right">
                            <flux:text class="font-medium">$89.99</flux:text>
                        </div>
                    </div>
                </div>
            </flux:card>
        </div>

        <!-- Recent Orders Table -->
        <flux:card>
            <flux:card.header>
                <flux:heading size="lg">Recent Orders</flux:heading>
                <flux:subheading>A list of your recent orders.</flux:subheading>
            </flux:card.header>

            <flux:table>
                <flux:columns>
                    <flux:column>Order</flux:column>
                    <flux:column>Status</flux:column>
                    <flux:column>Customer</flux:column>
                    <flux:column>Date</flux:column>
                    <flux:column>Amount</flux:column>
                </flux:columns>

                <flux:rows>
                    <flux:row>
                        <flux:cell variant="strong">#3210</flux:cell>
                        <flux:cell><flux:badge color="green" size="sm">Fulfilled</flux:badge></flux:cell>
                        <flux:cell>
                            <div class="flex items-center gap-3">
                                <flux:avatar size="xs" src="https://fluxui.dev/img/demo/user.jpg" />
                                <div>
                                    <div class="font-medium">Olivia Martin</div>
                                    <div class="text-sm text-zinc-500"><EMAIL></div>
                                </div>
                            </div>
                        </flux:cell>
                        <flux:cell variant="subtle">February 3, 2023</flux:cell>
                        <flux:cell variant="strong">$42.25</flux:cell>
                    </flux:row>

                    <flux:row>
                        <flux:cell variant="strong">#3209</flux:cell>
                        <flux:cell><flux:badge color="green" size="sm">Fulfilled</flux:badge></flux:cell>
                        <flux:cell>
                            <div class="flex items-center gap-3">
                                <flux:avatar size="xs" src="https://fluxui.dev/img/demo/user.jpg" />
                                <div>
                                    <div class="font-medium">Ava Johnson</div>
                                    <div class="text-sm text-zinc-500"><EMAIL></div>
                                </div>
                            </div>
                        </flux:cell>
                        <flux:cell variant="subtle">February 3, 2023</flux:cell>
                        <flux:cell variant="strong">$74.99</flux:cell>
                    </flux:row>

                    <flux:row>
                        <flux:cell variant="strong">#3208</flux:cell>
                        <flux:cell><flux:badge color="green" size="sm">Fulfilled</flux:badge></flux:cell>
                        <flux:cell>
                            <div class="flex items-center gap-3">
                                <flux:avatar size="xs" src="https://fluxui.dev/img/demo/user.jpg" />
                                <div>
                                    <div class="font-medium">Michael Johnson</div>
                                    <div class="text-sm text-zinc-500"><EMAIL></div>
                                </div>
                            </div>
                        </flux:cell>
                        <flux:cell variant="subtle">February 3, 2023</flux:cell>
                        <flux:cell variant="strong">$64.75</flux:cell>
                    </flux:row>

                    <flux:row>
                        <flux:cell variant="strong">#3207</flux:cell>
                        <flux:cell><flux:badge color="green" size="sm">Fulfilled</flux:badge></flux:cell>
                        <flux:cell>
                            <div class="flex items-center gap-3">
                                <flux:avatar size="xs" src="https://fluxui.dev/img/demo/user.jpg" />
                                <div>
                                    <div class="font-medium">Lisa Anderson</div>
                                    <div class="text-sm text-zinc-500"><EMAIL></div>
                                </div>
                            </div>
                        </flux:cell>
                        <flux:cell variant="subtle">February 3, 2023</flux:cell>
                        <flux:cell variant="strong">$34.50</flux:cell>
                    </flux:row>

                    <flux:row>
                        <flux:cell variant="strong">#3206</flux:cell>
                        <flux:cell><flux:badge color="green" size="sm">Fulfilled</flux:badge></flux:cell>
                        <flux:cell>
                            <div class="flex items-center gap-3">
                                <flux:avatar size="xs" src="https://fluxui.dev/img/demo/user.jpg" />
                                <div>
                                    <div class="font-medium">Chris Lee</div>
                                    <div class="text-sm text-zinc-500"><EMAIL></div>
                                </div>
                            </div>
                        </flux:cell>
                        <flux:cell variant="subtle">February 3, 2023</flux:cell>
                        <flux:cell variant="strong">$89.99</flux:cell>
                    </flux:row>
                </flux:rows>
            </flux:table>
        </flux:card>
    </flux:main>
</x-app-layout>
