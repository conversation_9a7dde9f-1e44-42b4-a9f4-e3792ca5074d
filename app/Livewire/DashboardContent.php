<?php

namespace App\Livewire;

use Livewire\Component;

class DashboardContent extends Component
{
    public $stats = [
        [
            'title' => 'Total Revenue',
            'value' => '$45,231.89',
            'change' => '+20.1%',
            'trend' => 'up',
            'icon' => 'currency-dollar'
        ],
        [
            'title' => 'Subscriptions',
            'value' => '+2350',
            'change' => '+180.1%',
            'trend' => 'up',
            'icon' => 'users'
        ],
        [
            'title' => 'Sales',
            'value' => '+12,234',
            'change' => '+19%',
            'trend' => 'up',
            'icon' => 'credit-card'
        ],
        [
            'title' => 'Active Now',
            'value' => '+573',
            'change' => '+201',
            'trend' => 'up',
            'icon' => 'activity'
        ]
    ];

    public $recentOrders = [
        [
            'id' => '#3210',
            'customer' => '<PERSON> Martin',
            'email' => '<EMAIL>',
            'status' => 'Fulfilled',
            'date' => 'February 3, 2023',
            'amount' => '$42.25'
        ],
        [
            'id' => '#3209',
            'customer' => '<PERSON> Johnson',
            'email' => '<EMAIL>',
            'status' => 'Fulfilled',
            'date' => 'February 3, 2023',
            'amount' => '$74.99'
        ],
        [
            'id' => '#3208',
            'customer' => 'Michael Johnson',
            'email' => '<EMAIL>',
            'status' => 'Fulfilled',
            'date' => 'February 3, 2023',
            'amount' => '$64.75'
        ],
        [
            'id' => '#3207',
            'customer' => 'Lisa Anderson',
            'email' => '<EMAIL>',
            'status' => 'Fulfilled',
            'date' => 'February 3, 2023',
            'amount' => '$34.50'
        ],
        [
            'id' => '#3206',
            'customer' => 'Chris Lee',
            'email' => '<EMAIL>',
            'status' => 'Fulfilled',
            'date' => 'February 3, 2023',
            'amount' => '$89.99'
        ]
    ];

    public function render()
    {
        return view('livewire.dashboard-content');
    }
}
