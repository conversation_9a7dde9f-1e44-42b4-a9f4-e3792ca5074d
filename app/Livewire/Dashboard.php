<?php

namespace App\Livewire;

use Livewire\Component;

class Dashboard extends Component
{
    public $searchQuery = '';
    public $selectedPeriod = 'January - June 2023';

    public $stats = [
        [
            'title' => 'Total Revenue',
            'value' => '$45,231.89',
            'change' => '+20.1%',
            'trend' => 'up',
            'icon' => 'currency-dollar'
        ],
        [
            'title' => 'Subscriptions',
            'value' => '+2350',
            'change' => '+180.1%',
            'trend' => 'up',
            'icon' => 'users'
        ],
        [
            'title' => 'Sales',
            'value' => '+12,234',
            'change' => '+19%',
            'trend' => 'up',
            'icon' => 'credit-card'
        ],
        [
            'title' => 'Active Now',
            'value' => '+573',
            'change' => '+201',
            'trend' => 'up',
            'icon' => 'signal'
        ]
    ];

    public $recentSales = [
        [
            'name' => '<PERSON>',
            'email' => '<EMAIL>',
            'amount' => '$42.25',
            'avatar' => 'https://fluxui.dev/img/demo/user.jpg'
        ],
        [
            'name' => '<PERSON> Johnson',
            'email' => '<EMAIL>',
            'amount' => '$74.99',
            'avatar' => 'https://fluxui.dev/img/demo/user.jpg'
        ],
        [
            'name' => 'Michael Johnson',
            'email' => '<EMAIL>',
            'amount' => '$64.75',
            'avatar' => 'https://fluxui.dev/img/demo/user.jpg'
        ],
        [
            'name' => 'Lisa Anderson',
            'email' => '<EMAIL>',
            'amount' => '$34.50',
            'avatar' => 'https://fluxui.dev/img/demo/user.jpg'
        ],
        [
            'name' => 'Chris Lee',
            'email' => '<EMAIL>',
            'amount' => '$89.99',
            'avatar' => 'https://fluxui.dev/img/demo/user.jpg'
        ]
    ];

    public $recentOrders = [
        [
            'id' => '#3210',
            'customer' => 'Olivia Martin',
            'email' => '<EMAIL>',
            'status' => 'Fulfilled',
            'date' => 'February 3, 2023',
            'amount' => '$42.25',
            'avatar' => 'https://fluxui.dev/img/demo/user.jpg'
        ],
        [
            'id' => '#3209',
            'customer' => 'Ava Johnson',
            'email' => '<EMAIL>',
            'status' => 'Fulfilled',
            'date' => 'February 3, 2023',
            'amount' => '$74.99',
            'avatar' => 'https://fluxui.dev/img/demo/user.jpg'
        ],
        [
            'id' => '#3208',
            'customer' => 'Michael Johnson',
            'email' => '<EMAIL>',
            'status' => 'Fulfilled',
            'date' => 'February 3, 2023',
            'amount' => '$64.75',
            'avatar' => 'https://fluxui.dev/img/demo/user.jpg'
        ],
        [
            'id' => '#3207',
            'customer' => 'Lisa Anderson',
            'email' => '<EMAIL>',
            'status' => 'Fulfilled',
            'date' => 'February 3, 2023',
            'amount' => '$34.50',
            'avatar' => 'https://fluxui.dev/img/demo/user.jpg'
        ],
        [
            'id' => '#3206',
            'customer' => 'Chris Lee',
            'email' => '<EMAIL>',
            'status' => 'Fulfilled',
            'date' => 'February 3, 2023',
            'amount' => '$89.99',
            'avatar' => 'https://fluxui.dev/img/demo/user.jpg'
        ]
    ];

    public function search()
    {
        // Handle search functionality
        $this->dispatch('search-performed', $this->searchQuery);
    }

    public function changePeriod($period)
    {
        $this->selectedPeriod = $period;
        // Handle period change logic here
    }

    public function render()
    {
        return view('livewire.dashboard');
    }
}
